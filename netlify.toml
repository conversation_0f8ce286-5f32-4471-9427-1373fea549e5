# Netlify Configuration for Goat Goat Admin Panel - Deploy v3
# Project: benevolent-toffee-58a972
# Admin URL: https://app.netlify.com/projects/benevolent-toffee-58a972
# This file configures the build and deployment settings for the Flutter Web admin panel

[build]
  # Static site deployment - no build process needed
  # Flutter web files are pre-built and committed to repository
  publish = "build/web"

  # Skip build process entirely
  command = ""

# Redirect rules for SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers for admin panel
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains"
    
    # Content Security Policy for Flutter Web - Permissive for Admin Panel
    Content-Security-Policy = "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval' data: blob:; style-src * 'unsafe-inline'; font-src * data: blob:; img-src * data: blob:; connect-src *; worker-src * blob: data:; child-src * blob: data:; object-src * data: blob:; base-uri 'self'; frame-ancestors 'none'; manifest-src *;"
    
    # Cache control
    Cache-Control = "public, max-age=31536000, immutable"

# MIME type and cache control for specific file types - CRITICAL FOR FLUTTER WEB
[[headers]]
  for = "*.html"
  [headers.values]
    Content-Type = "text/html; charset=utf-8"
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.dart.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/main.dart.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/flutter_service_worker.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.json"
  [headers.values]
    Content-Type = "application/json; charset=utf-8"

# Admin panel specific redirects
[[redirects]]
  from = "/admin"
  to = "/"
  status = 301

[[redirects]]
  from = "/login"
  to = "/"
  status = 301

# Development context (for branch deploys)
[context.branch-deploy]
  [context.branch-deploy.environment]
    ADMIN_ENVIRONMENT = "staging"
    ADMIN_URL = "https://deploy-preview-$DEPLOY_ID--your-site-name.netlify.app"

# Deploy preview context
[context.deploy-preview]
  [context.deploy-preview.environment]
    ADMIN_ENVIRONMENT = "preview"
    ADMIN_URL = "https://deploy-preview-$DEPLOY_ID--your-site-name.netlify.app"
