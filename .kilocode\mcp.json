{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "https://github.com/testingoat/goat_goat.git"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}}