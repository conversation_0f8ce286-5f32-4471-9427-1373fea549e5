// File generated by FlutterFire CLI.
// Ignore this file for code coverage, as it is generated code.

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;

// Default FirebaseOptions for use with your Firebase apps.
//
// Example:
// import 'package:firebase_core/firebase_core.dart';
// import 'firebase_options.dart';
//
// await Firebase.initializeApp(
//   options: DefaultFirebaseOptions.currentPlatform,
// );

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    // TODO: Replace with your actual Firebase configuration
    // This is a placeholder - you should run:
    // flutterfire configure
    // to generate the proper configuration
    
    return const FirebaseOptions(
      apiKey: 'AIzaSyAdWvAfr0pXWcPshAfZd1bdJejvUXlRaB8',
      appId: '1:188247457782:android:da1a4333f41579202f91d7',
      messagingSenderId: '188247457782',
      projectId: 'goat-goat-8e3da',
      storageBucket: 'goat-goat-8e3da.firebasestorage.app',
      // iOS specific
      iosClientId: '1:188247457782:ios:30281a5bef41d1882f91d7',
      iosBundleId: 'com.example.goatGoat',
    );
  }
}